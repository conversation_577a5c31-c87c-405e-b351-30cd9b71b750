

# Instagram Gamification

This is Instagram Gamification platfomr that allows user to create games for their shops on instagram and reward their followers.

## 🚀 Tech Stack
- [Next.js](https://nextjs.org/)
- [React](https://react.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [TypeScript](https://www.typescriptlang.org/)

## 📦 Installation

First, clone the repository:

```bash
git clone https://github.com/instagram-gamification/Develop.git
```

Navigate into the project folder:

```bash
cd Develop
```

Install the dependencies:

```bash
npm install
```
or
```bash
yarn install
```
or
```bash
pnpm install
```

## 🛠️ Running the Project

To run the development server:

```bash
npm run dev
```
or
```bash
yarn dev
```
or
```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.


## ⚙️ Environment Variables

Create a `.env.local` file in the root and add your environment variables like:

```
NEXTAUTH_SECRET=2x55sc0wLMPerozKkkIqAKTO5nVIHAXfSIIb+qUYqIA=
NEXTAUTH_URL=http://localhost:3000
MONGODB_URI=mongodb://localhost:27017/instagram-gamification
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## ✨ Features

- [x] SEO friendly
- [x] API Routes
- [x] Fully responsive
- [x] TypeScript support

