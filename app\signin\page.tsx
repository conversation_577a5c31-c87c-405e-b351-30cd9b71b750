'use client';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, ArrowLeft } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import SigninForm from '@/components/auth/SigninForm';

export default function SigninPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }
      `}</style>

      {/* Hero-style background with animated gradient */}
      <div
        className="fixed inset-0 bg-gradient-to-r opacity-90 animate-gradient"
        style={{
          backgroundImage: 'linear-gradient(to right, var(--primary), #F9A8D4)',
          animation: 'gradientShift 10s infinite linear',
        }}
      />

      <div className="relative z-10 flex flex-col min-h-screen">
        <header className="container py-4 sm:py-6">
          <div className="flex justify-between items-center">
            <Button
              variant="ghost"
              onClick={() => router.push('/')}
              className="flex items-center gap-2 text-white hover:bg-white/20"
            >
              <ArrowLeft className="h-4 w-4 rotate-180" />
              <span className="hidden sm:inline">بازگشت به خانه</span>
              <span className="sm:hidden">بازگشت</span>
            </Button>
          </div>
        </header>

        <main className="flex-1 container flex items-center justify-center py-4 px-4 sm:py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md mx-auto"
          >
            <Card className="backdrop-blur-sm border-none shadow-xl overflow-hidden w-full">
              <div className="absolute inset-0 bg-white/70 -z-10" />

              <CardHeader className="space-y-1 p-4 sm:p-6">
                <div className="flex items-center justify-center mb-2">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: 'spring', stiffness: 200, damping: 10 }}
                  >
                    <Sparkles className="h-8 w-8" style={{ color: 'var(--primary)' }} />
                  </motion.div>
                </div>
                <CardTitle className="text-2xl text-center" style={{ color: 'var(--text-primary)' }}>
                  خوش آمدید
                </CardTitle>
                <CardDescription className="text-center" style={{ color: 'var(--text-secondary)' }}>
                  برای ادامه وارد حساب کاربری خود شوید
                </CardDescription>
              </CardHeader>

              <CardContent className="p-4 sm:p-6">
                <SigninForm />
              </CardContent>

              <CardFooter className="flex justify-center p-4 sm:p-6">
                <p className="text-sm text-center" style={{ color: 'var(--text-secondary)' }}>
                  حساب کاربری ندارید؟{' '}
                  <Link href="/signup" className="font-medium" style={{ color: 'var(--primary)' }}>
                    ثبت نام
                  </Link>
                </p>
              </CardFooter>
            </Card>
          </motion.div>
        </main>
      </div>
    </div>
  );
}
