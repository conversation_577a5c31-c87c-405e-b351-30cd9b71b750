'use client';

import type React from 'react';
import type { Metadata } from 'next';
import { SessionProvider } from 'next-auth/react';
import './globals.css';
import { SidebarMobileProvider } from '@/hooks/use-sidebar-mobile';
import { AuthProvider } from '@/lib/auth-context';

const metadata: Metadata = {
  title: 'Instagram Gamification',
  description: 'Create engaging games for your Instagram followers',
  generator: 'v0.dev',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fa" dir="rtl">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@100;200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
      </head>
      <body style={{ fontFamily: 'Vazirmatn, Tahoma, sans-serif' }}>
        <SessionProvider>
          <AuthProvider>
            <SidebarMobileProvider>{children}</SidebarMobileProvider>
          </AuthProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
