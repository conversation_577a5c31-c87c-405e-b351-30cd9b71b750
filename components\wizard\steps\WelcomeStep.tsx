'use client';

import { motion } from 'framer-motion';
import { Spark<PERSON>, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface WelcomeStepProps {
  onNext: () => void;
}

export default function WelcomeStep({ onNext }: WelcomeStepProps) {
  return (
    <div className="text-center py-8">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: 'spring', stiffness: 200, damping: 10 }}
        className="flex justify-center mb-6"
      >
        <div className="relative">
          <div
            className="w-24 h-24 rounded-full flex items-center justify-center animate-spin-slow"
            style={{ backgroundColor: 'rgba(139, 92, 246, 0.2)' }}
          >
            <Sparkles className="h-12 w-12" style={{ color: 'var(--primary)' }} />
          </div>
          <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
            <Sparkles className="h-4 w-4 text-white" />
          </div>
        </div>
      </motion.div>

      <motion.h1
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="text-3xl font-bold mb-4"
        style={{ color: 'var(--text-primary)' }}
      >
        به اینستاگیمیفای خوش آمدید
      </motion.h1>

      <motion.p
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="text-lg mb-8 max-w-xl mx-auto"
        style={{ color: 'var(--text-secondary)' }}
      >
        اینجا می‌توانید بازی هیجان‌انگیزی برای استوری‌های اینستاگرام خود بسازید و فالوورهایتان را با شور و شوق درگیر
        کنید!
      </motion.p>

      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.6, type: 'spring' }}
        className="mb-8"
      >
        <div className="relative w-64 h-64 mx-auto">
          <div
            className="absolute inset-0 rounded-full animate-spin-slow"
            style={{
              background: 'conic-gradient(from 0deg, #8b5cf6, #ec4899, #f97316, #84cc16, #8b5cf6)',
              clipPath: 'circle(50% at center)',
            }}
          />
          <div className="absolute inset-2 rounded-full bg-white flex items-center justify-center">
            <div className="text-center">
              <p className="text-xl font-bold" style={{ color: 'var(--primary)' }}>
                بچرخان و ببر!
              </p>
              <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                فالوورهایت را درگیر کن
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button
          size="lg"
          onClick={onNext}
          className="font-semibold text-lg px-8"
          style={{ backgroundColor: 'var(--secondary)', color: 'var(--button-text)' }}
        >
          بیایید شروع کنیم!
          <ArrowRight className="mr-2 h-5 w-5 rotate-180" />
        </Button>
      </motion.div>
    </div>
  );
}
