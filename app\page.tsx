'use client';

import React from 'react';

import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion, useAnimation, useInView } from 'framer-motion';
import confetti from 'canvas-confetti';
import {
  ArrowRight,
  Award,
  BarChart3,
  CheckCircle,
  ChevronRight,
  Clock,
  Cog,
  Compass,
  Gift,
  Globe,
  Heart,
  LinkIcon,
  MessageCircle,
  Palette,
  Share2,
  Shield,
  Sparkles,
  Zap,
  Star,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

export default function Home() {
  const router = useRouter();

  return (
    <>
      <style jsx global>{`
        :root {
          --background: #ffffff;
          --text-primary: #334155;
          --text-secondary: #64748b;
          --primary: #8b5cf6;
          --secondary: #84cc16;
          --card-bg: #fafafa;
          --button-bg: #8b5cf6;
          --button-text: #ffffff;
          --border: #e5e7eb;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        body {
          background-color: var(--background);
          color: var(--text-primary);
          font-family: 'Vazirmatn', 'Tahoma', sans-serif;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          color: var(--text-primary);
        }

        p,
        span {
          color: var(--text-secondary);
        }
      `}</style>
      <div className="flex min-h-screen flex-col" dir="rtl">
        <header
          className="sticky top-0 z-50 w-full border-b backdrop-blur-sm"
          style={{ backgroundColor: 'var(--background)', borderColor: 'var(--border)' }}
        >
          <div className="container flex h-16 items-center justify-between">
            <div className="flex items-center gap-2 font-bold text-xl">
              <Sparkles className="h-6 w-6" style={{ color: 'var(--primary)' }} />
              <span>اینستاگیمیفای</span>
            </div>
            <nav className="hidden md:flex items-center gap-6">
              {[
                { id: 'features', name: 'ویژگی‌ها' },
                { id: 'how-it-works', name: 'نحوه کار' },
                { id: 'pricing', name: 'قیمت‌گذاری' },
                { id: 'testimonials', name: 'نظرات کاربران' },
              ].map((section) => (
                <button
                  key={section.id}
                  onClick={() => {
                    const element = document.getElementById(section.id);
                    element?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  className="text-sm font-medium transition-colors hover:text-primary"
                  style={{ color: 'var(--text-primary)' }}
                >
                  {section.name}
                </button>
              ))}
            </nav>
            <div className="flex items-center gap-4">
              <Link
                href="/signin"
                className="hidden sm:inline-flex h-9 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1"
                style={{ borderColor: 'var(--primary)', color: 'var(--primary)' }}
              >
                ورود
              </Link>
              <Button
                onClick={() => router.push('/signup')}
                style={{ backgroundColor: 'var(--button-bg)', color: 'var(--button-text)' }}
              >
                شروع کنید
              </Button>
            </div>
          </div>
        </header>
        <main className="flex-1">
          <HeroSection />
          <VisualHookSection />
          <FeaturesSection />
          <HowItWorksSection />
          <TestimonialsSection />
          <PricingSection />
          <TrustSignalsSection />
          <FooterCTASection />
        </main>
        <Footer />
      </div>
    </>
  );
}
function HeroSection() {
  const router = useRouter();

  return (
    <section className="relative overflow-hidden">
      <div
        className="absolute inset-0 bg-gradient-to-r opacity-90 animate-gradient"
        style={{
          backgroundImage: 'linear-gradient(to right, var(--primary), #F9A8D4)',
          animation: 'gradientShift 10s infinite linear',
        }}
      />
      <div className="container relative z-10 py-24 md:py-32">
        <div className="grid gap-8 md:grid-cols-2 items-center">
          <div className="space-y-6">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, type: 'spring', bounce: 0.5 }}
              className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl"
              style={{ color: 'var(--text-primary)' }}
            >
              فالوورهای خود را با بازی‌های جذاب به مشتری تبدیل کنید!
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.5, ease: 'easeInOut' }}
              className="text-xl"
              style={{ color: 'var(--text-secondary)' }}
            >
              در چند دقیقه بازی‌های سفارشی برای فروشگاه اینستاگرام خود بسازید
            </motion.p>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button
                size="lg"
                className="font-semibold text-lg animate-pulse-subtle shadow-lg"
                style={{ backgroundColor: 'var(--secondary)', color: 'var(--button-text)' }}
                onClick={() => router.push('/signup')}
              >
                همین امروز رایگان شروع کنید
                <ArrowRight className="mr-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                style={{
                  backgroundColor: 'var(--card-bg)',
                  color: 'var(--text-primary)',
                  borderColor: 'var(--border)',
                }}
              >
                مشاهده دمو
              </Button>
            </motion.div>
          </div>
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="relative h-[400px] w-full rounded-lg p-1 backdrop-blur-sm"
            style={{ backgroundColor: 'var(--card-bg)' }}
          >
            <div className="absolute inset-0 rounded-lg border-2" style={{ borderColor: 'var(--border)' }} />
            <div className="relative h-full w-full rounded-md overflow-hidden">
              <div
                className="absolute inset-0 bg-gradient-to-br"
                style={{ backgroundImage: 'linear-gradient(to bottom right, var(--primary) 20%, #F9A8D4 20%)' }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="grid grid-cols-2 gap-4 p-6 w-full max-w-md">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div
                      key={i}
                      className="bg-white rounded-lg shadow-lg p-4"
                      style={{ transform: `rotate(${i % 2 === 0 ? -3 : 3}deg)` }}
                    >
                      <div
                        className="w-full h-24 rounded-md mb-2"
                        style={{ backgroundColor: ['#2DD4BF', '#FACC15', '#F97316', '#84CC16'][i] }}
                      ></div>
                      <div className="h-4 w-3/4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 w-1/2 bg-gray-200 rounded"></div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

function VisualHookSection() {
  const [selectedGame, setSelectedGame] = useState('wheel');
  const wheelRef = useRef(null);
  const [isHovering, setIsHovering] = useState(false);

  const handleHover = () => {
    setIsHovering(true);
    if (typeof window !== 'undefined' && wheelRef.current) {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
      });
    }
  };

  const games = [
    { id: 'wheel', name: 'چرخ شانس', icon: Compass },
    { id: 'scratch', name: 'کارت خراش', icon: Palette },
    { id: 'lever', name: 'اهرم شانس', icon: Zap },
    { id: 'hunt', name: 'شکار گنج', icon: Gift },
  ];

  return (
    <section style={{ backgroundColor: 'var(--card-bg)', padding: '5rem 0' }}>
      <div className="container">
        <div className="grid gap-12 md:grid-cols-2 items-center">
          <div className="space-y-6">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="text-3xl font-bold tracking-tight sm:text-4xl"
              style={{ color: 'var(--text-primary)' }}
            >
              فالوورهای خود را با بازی‌های تعاملی درگیر کنید
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-lg"
              style={{ color: 'var(--text-secondary)' }}
            >
              از انواع مختلف بازی‌ها انتخاب کنید تا تجربه‌های سفارشی بسازید که فالوورهای اینستاگرام شما عاشق بازی کردن
              آن‌ها خواهند بود.
            </motion.p>
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4 }}
              className="flex flex-wrap gap-3"
            >
              {games.map((game) => (
                <Button
                  key={game.id}
                  variant={selectedGame === game.id ? 'default' : 'outline'}
                  className="flex items-center gap-2"
                  style={{
                    backgroundColor: selectedGame === game.id ? 'var(--primary)' : 'var(--card-bg)',
                    color: selectedGame === game.id ? 'var(--button-text)' : 'var(--primary)',
                    borderColor: 'var(--primary)',
                  }}
                  onClick={() => setSelectedGame(game.id)}
                >
                  <game.icon className="h-4 w-4" />
                  {game.name}
                </Button>
              ))}
            </motion.div>
            <motion.ul
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4 }}
              className="space-y-3"
            >
              {[
                'بازی‌های کاملاً قابل تنظیم',
                'تحویل فوری جایزه',
                'پیگیری مشارکت در زمان واقعی',
                'افزایش نرخ تبدیل شما',
              ].map((item, i) => (
                <motion.li
                  key={i}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.5 + i * 0.1 }}
                  className="flex items-center gap-2"
                >
                  <CheckCircle className="h-5 w-5" style={{ color: 'var(--secondary)' }} />
                  <span>{item}</span>
                </motion.li>
              ))}
            </motion.ul>
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.8 }}
            >
              <Button style={{ backgroundColor: 'var(--primary)', color: 'var(--button-text)' }}>
                اولین بازی خود را بسازید
              </Button>
            </motion.div>
          </div>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="relative mx-auto max-w-sm"
          >
            <div
              className="relative rounded-xl border-4 p-4 shadow-lg"
              style={{ borderColor: 'var(--border)', backgroundColor: 'var(--card-bg)' }}
            >
              <div className="absolute -top-2 -left-2 h-6 w-6 rounded-full" style={{ backgroundColor: '#F97316' }} />
              <div className="absolute -top-2 -right-2 h-6 w-6 rounded-full" style={{ backgroundColor: '#F97316' }} />
              <div className="absolute -bottom-2 -left-2 h-6 w-6 rounded-full" style={{ backgroundColor: '#F97316' }} />
              <div
                className="absolute -bottom-2 -right-2 h-6 w-6 rounded-full"
                style={{ backgroundColor: '#F97316' }}
              />
              <div className="mb-4 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded-full" style={{ backgroundColor: 'var(--card-bg)' }} />
                  <span className="font-medium">your_shop</span>
                </div>
                <div className="flex gap-2">
                  <Heart className="h-5 w-5" />
                  <MessageCircle className="h-5 w-5" />
                  <Share2 className="h-5 w-5" />
                </div>
              </div>

              {selectedGame === 'wheel' && (
                <div
                  ref={wheelRef}
                  onMouseEnter={handleHover}
                  onMouseLeave={() => setIsHovering(false)}
                  className={`relative mx-auto h-64 w-64 rounded-full ${
                    isHovering ? '' : 'animate-spin-slow'
                  } cursor-pointer`}
                  style={{ backgroundColor: '#2DD4BF' }}
                >
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div
                      key={i}
                      className="absolute inset-0 origin-center"
                      style={{ transform: `rotate(${i * 45}deg)` }}
                    >
                      <div
                        className={`h-32 w-1 ${i % 2 === 0 ? 'bg-white' : 'bg-[#FACC15]'}`}
                        style={{ marginLeft: 'calc(50% - 0.5px)' }}
                      />
                    </div>
                  ))}
                  <div
                    className="absolute inset-0 flex items-center justify-center rounded-full backdrop-blur-sm"
                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                  >
                    {isHovering ? (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: 'spring', bounce: 0.5 }}
                        className="flex flex-col items-center justify-center gap-2 rounded-lg p-4 shadow-lg"
                        style={{ backgroundColor: 'var(--card-bg)' }}
                      >
                        <span className="text-xl font-bold" style={{ color: '#F97316' }}>
                          20% OFF!
                        </span>
                        <span className="text-sm">You won a discount!</span>
                      </motion.div>
                    ) : (
                      <div className="h-16 w-16 rounded-full shadow-md" style={{ backgroundColor: 'var(--card-bg)' }} />
                    )}
                  </div>
                </div>
              )}

              {selectedGame === 'scratch' && (
                <div
                  className="relative mx-auto h-64 w-64 rounded-lg flex items-center justify-center cursor-pointer"
                  style={{ backgroundColor: '#A5F3FC' }}
                >
                  <div className="absolute inset-0 bg-[url('/placeholder.svg?height=256&width=256')] bg-cover bg-center opacity-30" />
                  <div className="text-center p-6 z-10">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="rounded-lg p-4 shadow-lg"
                      style={{ backgroundColor: 'var(--card-bg)' }}
                    >
                      <h3 className="font-bold text-lg mb-2">Scratch & Win</h3>
                      <p className="text-sm mb-4">Scratch the card to reveal your prize!</p>
                      <div
                        className="h-20 w-full rounded flex items-center justify-center"
                        style={{ backgroundColor: 'var(--card-bg)' }}
                      >
                        <span style={{ color: 'var(--text-secondary)' }}>Scratch Here</span>
                      </div>
                    </motion.div>
                  </div>
                </div>
              )}

              {selectedGame === 'lever' && (
                <div
                  className="relative mx-auto h-64 w-64 rounded-lg flex items-center justify-center cursor-pointer"
                  style={{ backgroundColor: '#FEF3C7' }}
                >
                  <div
                    className="h-full w-32 mx-auto rounded-lg flex flex-col items-center justify-between py-4"
                    style={{ backgroundColor: 'rgba(249, 115, 22, 0.2)' }}
                  >
                    <div
                      className="w-16 h-16 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: '#F97316' }}
                    >
                      <Star className="h-8 w-8 text-white" />
                    </div>
                    <motion.div
                      whileHover={{ y: 20 }}
                      className="w-8 h-32 rounded-full relative"
                      style={{ backgroundColor: '#F97316' }}
                    >
                      <div
                        className="absolute bottom-0 w-full h-8 rounded-full"
                        style={{ backgroundColor: '#FACC15' }}
                      />
                    </motion.div>
                    <div className="text-center">
                      <p className="font-bold">بکش و ببر!</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedGame === 'hunt' && (
                <div
                  className="relative mx-auto h-64 w-64 rounded-lg p-4 cursor-pointer"
                  style={{ backgroundColor: 'rgba(196, 181, 253, 0.3)' }}
                >
                  <div className="grid grid-cols-3 grid-rows-3 gap-2 h-full">
                    {Array.from({ length: 9 }).map((_, i) => (
                      <motion.div
                        key={i}
                        whileHover={{ scale: 1.1 }}
                        className="rounded-md shadow flex items-center justify-center"
                        style={{ backgroundColor: 'var(--card-bg)' }}
                      >
                        {i === 4 ? (
                          <Gift className="h-8 w-8" style={{ color: '#EC4899' }} />
                        ) : (
                          <div className="h-6 w-6 rounded-full" style={{ backgroundColor: 'var(--card-bg)' }} />
                        )}
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              <div className="mt-4 flex items-center justify-between">
                <span className="text-sm font-medium">
                  {selectedGame === 'wheel' && 'بچرخان و جایزه ببر!'}
                  {selectedGame === 'scratch' && 'بخراش و جایزه‌ات را کشف کن!'}
                  {selectedGame === 'lever' && 'اهرم را بکش و ببر!'}
                  {selectedGame === 'hunt' && 'گنج را پیدا کن و ببر!'}
                </span>
                <Button size="sm" style={{ backgroundColor: 'var(--secondary)', color: 'var(--button-text)' }}>
                  همین حالا بازی کن
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
function FeaturesSection() {
  const features = [
    {
      title: 'سفارشی‌سازی کامل',
      description: 'تمام جنبه‌های بازی‌هایتان را از جوایز تا عناصر طراحی سفارشی کنید.',
      icon: Palette,
    },
    {
      title: 'لینک‌های فوری',
      description: 'در چند ثانیه لینک‌های قابل اشتراک برای پست در اینستاگرام بسازید.',
      icon: LinkIcon,
    },
    {
      title: 'افزایش مشارکت',
      description: 'تعامل فالوورها و زمان صرف شده با برند شما را افزایش دهید.',
      icon: Zap,
    },
    {
      title: 'داشبورد زمان واقعی',
      description: 'عملکرد و معیارهای مشارکت را همان‌طور که اتفاق می‌افتد دنبال کنید.',
      icon: BarChart3,
    },
  ];

  return (
    <section id="features" style={{ backgroundColor: 'var(--card-bg)', padding: '5rem 0' }}>
      <div className="container">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold tracking-tight sm:text-4xl"
            style={{ color: 'var(--text-primary)' }}
          >
            ویژگی‌های قدرتمند برای صاحبان فروشگاه اینستاگرام
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-4 text-lg max-w-2xl mx-auto"
            style={{ color: 'var(--text-secondary)' }}
          >
            همه چیزی که برای ایجاد بازی‌های جذاب که فالوورها را به مشتری تبدیل می‌کند نیاز دارید
          </motion.p>
        </div>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {features.map((feature, i) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, scale: 0 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: i * 0.2 }}
              whileHover={{ translateY: -10 }}
              className="group"
            >
              <Card
                className="h-full border-none shadow-md hover:shadow-xl transition-shadow duration-300"
                style={{ backgroundColor: 'var(--card-bg)' }}
              >
                <CardHeader>
                  <div
                    className="mb-2 inline-flex h-12 w-12 items-center justify-center rounded-lg"
                    style={{ backgroundColor: 'rgba(132, 204, 22, 0.1)' }}
                  >
                    <feature.icon className="h-6 w-6" style={{ color: 'var(--secondary)' }} />
                  </div>
                  <CardTitle style={{ color: 'var(--text-primary)' }}>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p style={{ color: 'var(--text-secondary)' }}>{feature.description}</p>
                </CardContent>
                <CardFooter>
                  <Button
                    variant="ghost"
                    className="p-0 hover:bg-transparent group-hover:underline"
                    style={{ color: 'var(--secondary)' }}
                  >
                    بیشتر بدانید <ChevronRight className="mr-1 h-4 w-4 rotate-180" />
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

function HowItWorksSection() {
  const steps = [
    {
      title: 'بازی انتخاب کنید',
      description: 'از چرخ‌های دوار، کارت‌های خراش، شکار گنج و موارد دیگر انتخاب کنید.',
      icon: Compass,
      color: '#FEF3C7',
    },
    {
      title: 'سفارشی کنید',
      description: 'جوایز، نرخ برد، رنگ‌ها و برندینگ را متناسب با فروشگاه خود تنظیم کنید.',
      icon: Cog,
      color: '#A5F3FC',
    },
    {
      title: 'لینک را به اشتراک بگذارید',
      description: 'لینک بازی منحصر به فرد خود را در استوری‌های اینستاگرام یا بیو خود پست کنید.',
      icon: Share2,
      color: '#FEF3C7',
    },
    {
      title: 'بازیکنان را پاداش دهید',
      description: 'به طور خودکار جوایز را از طریق پیامک، پیام مستقیم یا ایمیل به برندگان ارسال کنید.',
      icon: Gift,
      color: '#A5F3FC',
    },
  ];

  const progressRef = useRef(null);
  const isInView = useInView(progressRef, { once: true, amount: 0.5 });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start({ width: '100%' });
    }
  }, [isInView, controls]);

  return (
    <section id="how-it-works" style={{ backgroundColor: 'var(--background)', padding: '5rem 0' }}>
      <div className="container">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold tracking-tight sm:text-4xl"
            style={{ color: 'var(--text-primary)' }}
          >
            نحوه کار
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-4 text-lg max-w-2xl mx-auto"
            style={{ color: 'var(--text-secondary)' }}
          >
            اولین بازی خود را در چند دقیقه بسازید و به اشتراک بگذارید
          </motion.p>
        </div>
        <div className="relative">
          <div ref={progressRef} className="absolute left-0 top-1/2 h-1 w-full -translate-y-1/2">
            <motion.div
              initial={{ width: 0 }}
              animate={controls}
              transition={{ duration: 1.5, ease: 'easeInOut' }}
              className="h-full"
              style={{ backgroundColor: '#2DD4BF' }}
            />
          </div>
          <div className="relative z-10 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {steps.map((step, i) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, x: i % 2 === 0 ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, type: 'spring', bounce: 0.4, delay: i * 0.1 }}
              >
                <Card className="h-full border-none" style={{ backgroundColor: step.color }}>
                  <CardHeader className="pb-2">
                    <div className="mb-4 flex items-center gap-4">
                      <div
                        className="flex h-10 w-10 items-center justify-center rounded-full"
                        style={{ backgroundColor: 'var(--card-bg)' }}
                      >
                        <motion.div
                          initial={{ rotate: 0 }}
                          whileInView={{ rotate: 180 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.6, delay: 0.2 + i * 0.1 }}
                        >
                          <step.icon className="h-5 w-5" style={{ color: 'var(--text-primary)' }} />
                        </motion.div>
                      </div>
                      <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                        Step {i + 1}
                      </span>
                    </div>
                    <CardTitle style={{ color: 'var(--text-primary)' }}>{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p style={{ color: 'var(--text-secondary)' }}>{step.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

function TestimonialsSection() {
  const testimonials = [
    {
      name: 'سارا ج.',
      role: 'صاحب بوتیک مد',
      content:
        "از زمان استفاده از این پلتفرم، مشارکت من <span class='text-[#F97316] font-medium'>۵۰٪</span> افزایش یافته و فروش ۳۰٪ بالا رفته! بازی چرخ دوار در میان فالوورهایم بسیار محبوب است.",
      avatar: '/placeholder.svg?height=80&width=80',
    },
    {
      name: 'مایکل ت.',
      role: 'فروشگاه جواهرات دست‌ساز',
      content:
        "ابتدا شک داشتم، اما پس از فقط یک هفته، <span class='text-[#F97316] font-medium'>۳ برابر افزایش تبدیل</span> دیدم. داشبورد ردیابی همه چیز را آسان می‌کند.",
      avatar: '/placeholder.svg?height=80&width=80',
    },
    {
      name: 'النا ر.',
      role: 'فروشگاه محصولات زیبایی',
      content:
        "فالوورهایم عاشق بازی کارت خراش هستند! <span class='text-[#F97316] font-medium'>صدها مشتری جدید</span> آورده که ممکن بود در غیر این صورت خرید نکنند.",
      avatar: '/placeholder.svg?height=80&width=80',
    },
  ];

  const stats = [
    { value: 50, label: 'مشارکت بیشتر' },
    { value: 3, label: 'برابر تبدیل بالاتر' },
    { value: 70, label: 'زمان بیشتر صرف شده' },
  ];

  return (
    <section id="testimonials" style={{ backgroundColor: 'rgba(167, 243, 208, 0.3)', padding: '5rem 0' }}>
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl" style={{ color: 'var(--text-primary)' }}>
            نظرات کاربران ما
          </h2>
          <p className="mt-4 text-lg max-w-2xl mx-auto" style={{ color: 'var(--text-secondary)' }}>
            به صدها صاحب فروشگاه اینستاگرام که در حال رشد کسب‌وکار خود هستند بپیوندید
          </p>
        </div>
        <div className="grid gap-12 lg:grid-cols-2">
          <div className="space-y-8">
            {testimonials.map((testimonial, i) => (
              <div key={i} className="p-6 rounded-xl shadow-md" style={{ backgroundColor: 'var(--card-bg)' }}>
                <div className="mb-4 flex items-center gap-4">
                  <div className="h-12 w-12 overflow-hidden rounded-full">
                    <Image
                      src={testimonial.avatar || '/placeholder.svg'}
                      alt={testimonial.name}
                      width={48}
                      height={48}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="font-semibold" style={{ color: 'var(--text-primary)' }}>
                      {testimonial.name}
                    </h4>
                    <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                      {testimonial.role}
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <span className="absolute -left-2 -top-2 text-4xl" style={{ color: 'rgba(249, 115, 22, 0.3)' }}>
                    "
                  </span>
                  <p
                    className="text-lg"
                    style={{ color: 'var(--text-primary)' }}
                    dangerouslySetInnerHTML={{ __html: testimonial.content }}
                  />
                  <span className="absolute -bottom-4 -right-2 text-4xl" style={{ color: 'rgba(249, 115, 22, 0.3)' }}>
                    "
                  </span>
                </div>
              </div>
            ))}
          </div>
          <div className="grid gap-6 sm:grid-cols-3">
            {stats.map((stat, i) => (
              <div
                key={i}
                className="flex flex-col items-center justify-center rounded-lg p-6 shadow-md"
                style={{ backgroundColor: 'var(--card-bg)' }}
              >
                <div className="flex items-end gap-1">
                  <span className="text-4xl font-bold" style={{ color: '#F97316' }}>
                    {stat.value}
                  </span>
                  <span className="text-xl font-bold" style={{ color: '#F97316' }}>
                    {stat.value > 10 ? '%' : 'x'}
                  </span>
                </div>
                <p className="mt-2 text-center text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                  {stat.label}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

function PricingSection() {
  const plans = [
    {
      name: 'رایگان',
      price: '0',
      description: 'عالی برای شروع',
      features: ['۱ نوع بازی', 'سفارشی‌سازی پایه', '۱۰۰ بازی در ماه', 'پشتیبانی ایمیل'],
      cta: 'شروع رایگان',
      popular: false,
    },
    {
      name: 'رشد',
      price: '29',
      description: 'برای فروشگاه‌های اینستاگرام در حال رشد',
      features: ['همه انواع بازی', 'سفارشی‌سازی کامل', '۱٬۰۰۰ بازی در ماه', 'پشتیبانی اولویت‌دار', 'داشبورد تحلیل‌ها'],
      cta: 'شروع آزمایش ۱۴ روزه',
      popular: true,
    },
    {
      name: 'حرفه‌ای',
      price: '79',
      description: 'برای کسب‌وکارهای تثبیت شده',
      features: [
        'همه انواع بازی',
        'سفارشی‌سازی پیشرفته',
        'بازی نامحدود',
        'پشتیبانی اولویت‌دار',
        'تحلیل‌های پیشرفته',
        'برندینگ سفارشی',
        'دسترسی API',
      ],
      cta: 'تماس با فروش',
      popular: false,
    },
  ];

  return (
    <section id="pricing" style={{ backgroundColor: 'rgba(229, 231, 235, 0.3)', padding: '5rem 0' }}>
      <div className="container">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold tracking-tight sm:text-4xl"
            style={{ color: 'var(--text-primary)' }}
          >
            قیمت‌گذاری ساده و شفاف
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-4 text-lg max-w-2xl mx-auto"
            style={{ color: 'var(--text-secondary)' }}
          >
            با پلن رایگان ما شروع کنید و همراه با رشدتان ارتقا دهید
          </motion.p>
        </div>
        <div className="grid gap-8 md:grid-cols-3">
          {plans.map((plan, i) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: i * 0.3 }}
              className="relative"
            >
              <Card
                className={`h-full flex flex-col ${plan.popular ? 'shadow-lg' : ''}`}
                style={{
                  backgroundColor: 'var(--card-bg)',
                  borderColor: plan.popular ? 'var(--secondary)' : 'var(--border)',
                }}
              >
                {plan.popular && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: 'spring', bounce: 0.5, delay: 0.5 }}
                    className="absolute -top-3 right-4 rounded-full px-3 py-1 text-xs font-semibold"
                    style={{ backgroundColor: 'var(--secondary)', color: 'var(--button-text)' }}
                  >
                    محبوب‌ترین
                  </motion.div>
                )}
                <CardHeader>
                  <CardTitle className="text-2xl" style={{ color: 'var(--text-primary)' }}>
                    {plan.name}
                  </CardTitle>
                  <CardDescription style={{ color: 'var(--text-secondary)' }}>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent className="flex-grow space-y-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold" style={{ color: 'var(--text-primary)' }}>
                      ${plan.price}
                    </span>
                    <span className="ml-1" style={{ color: 'var(--text-secondary)' }}>
                      /ماه
                    </span>
                  </div>
                  <ul className="space-y-3">
                    {plan.features.map((feature, j) => (
                      <li key={j} className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5" style={{ color: 'var(--secondary)' }} />
                        <span style={{ color: 'var(--text-primary)' }}>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter className="pt-4 mt-auto">
                  <Button
                    className="w-full"
                    style={{
                      backgroundColor: plan.popular ? 'var(--secondary)' : 'var(--card-bg)',
                      color: plan.popular ? 'var(--button-text)' : 'var(--secondary)',
                      borderColor: 'var(--secondary)',
                    }}
                  >
                    {plan.cta}
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

function TrustSignalsSection() {
  const logos = Array(6).fill('/placeholder-logo.svg');
  const icons = [Shield, Clock, Award]; // آرایه آیکون‌ها رو جدا تعریف می‌کنیم

  return (
    <section style={{ backgroundColor: 'var(--background)', padding: '4rem 0' }}>
      <div className="container">
        <motion.h3
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center text-xl font-medium mb-10"
          style={{ color: 'var(--text-primary)' }}
        >
          مورد اعتماد صاحبان فروشگاه اینستاگرام در سراسر جهان
        </motion.h3>
        <div className="grid grid-cols-2 gap-8 md:grid-cols-3 lg:grid-cols-6">
          {logos.map((logo, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
              className="flex items-center justify-center"
            >
              <Image
                src={logo || '/placeholder.svg'}
                alt="Company logo"
                width={120}
                height={40}
                className="h-12 w-auto grayscale opacity-70 transition-opacity hover:opacity-100"
              />
            </motion.div>
          ))}
        </div>
        <div className="mt-16 flex flex-col items-center justify-center gap-4 md:flex-row">
          {['امن و قابل اعتماد', 'پشتیبانی ۲۴/۷', 'پلتفرم برنده جایزه'].map((text, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
              className="flex items-center gap-2 rounded-full px-4 py-2"
              style={{ backgroundColor: 'rgba(96, 165, 250, 0.1)' }}
            >
              {/* استفاده مستقیم از کامپوننت آیکون */}
              {React.createElement(icons[i], { className: 'h-5 w-5', style: { color: '#60A5FA' } })}
              <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                {text}
              </span>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

function FooterCTASection() {
  const router = useRouter();

  return (
    <section className="relative overflow-hidden" style={{ backgroundColor: '#6D28D9', padding: '5rem 0' }}>
      <div
        className="absolute inset-0 opacity-10"
        style={{ backgroundImage: 'linear-gradient(45deg, #6D28D9, #EC4899)' }}
      />
      <div className="container relative z-10">
        <div className="mx-auto max-w-3xl text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold tracking-tight sm:text-4xl"
            style={{ color: 'var(--text-primary)' }}
          >
            آماده تحول فروشگاه اینستاگرام خود هستید؟
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-4 text-lg"
            style={{ color: 'var(--text-secondary)' }}
          >
            همین امروز شروع به ایجاد بازی‌های جذاب کنید که فالوورها را به مشتری تبدیل می‌کند.
          </motion.p>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-8"
          >
            <Button
              size="lg"
              className="font-semibold text-lg animate-scale-subtle"
              style={{ backgroundColor: '#EC4899', color: 'var(--button-text)' }}
              onClick={() => router.push('/signup')}
            >
              همین حالا فروشگاهت را گیمیفای کن!
              <Sparkles className="mr-2 h-5 w-5" />
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

function Footer() {
  return (
    <footer style={{ backgroundColor: 'var(--card-bg)' }}>
      <div className="container py-12">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          <div>
            <div className="flex items-center gap-2 font-bold text-xl mb-4">
              <Sparkles className="h-6 w-6" style={{ color: '#67E8F9' }} />
              <span style={{ color: 'var(--text-primary)' }}>اینستاگیمیفای</span>
            </div>
            <p style={{ color: 'var(--text-secondary)', maxWidth: '16rem' }}>
              نهایی‌ترین پلتفرم گیمیفیکیشن برای صاحبان فروشگاه اینستاگرام جهت افزایش مشارکت و فروش.
            </p>
            <div className="mt-4 flex gap-4">
              {[
                Globe,
                { path: 'M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z' },
                { path: 'M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z M17.5 6.5h.01' },
                {
                  path: 'M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z',
                },
              ].map((icon, i) => (
                <Link key={i} href="#" className="transition-colors" style={{ color: 'var(--text-secondary)' }}>
                  {i === 0 ? (
                    <Globe className="h-5 w-5" />
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      {i === 1 ? (
                        <path d={icon.path} />
                      ) : i === 2 ? (
                        <>
                          <rect x="2" y="2" width="20" height="20" rx="5" ry="5" />
                          <path d={icon.path.split(' ')[0]} />
                          <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" />
                        </>
                      ) : (
                        <path d={icon.path} />
                      )}
                    </svg>
                  )}
                </Link>
              ))}
            </div>
          </div>
          {[
            { title: 'محصول', items: ['ویژگی‌ها', 'بازی‌ها', 'قیمت‌گذاری', 'نظرات', 'مطالعات موردی'] },
            { title: 'منابع', items: ['مستندات', 'بلاگ', 'آموزش‌ها', 'API', 'پشتیبانی'] },
            { title: 'شرکت', items: ['درباره ما', 'شغل‌ها', 'تماس', 'حریم خصوصی', 'شرایط خدمات'] },
          ].map((section, i) => (
            <div key={i}>
              <h3 className="font-semibold text-lg mb-4" style={{ color: 'var(--text-primary)' }}>
                {section.title}
              </h3>
              <ul className="space-y-2">
                {section.items.map((item) => (
                  <li key={item}>
                    <Link
                      href="#"
                      className="transition-colors hover:underline"
                      style={{ color: 'var(--text-secondary)' }}
                    >
                      {item}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        <div
          className="mt-12 pt-8 flex flex-col md:flex-row justify-between items-center"
          style={{ borderTopColor: 'var(--border)' }}
        >
          <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
            © {new Date().getFullYear()} اینستاگیمیفای. تمام حقوق محفوظ است.
          </p>
          <div className="mt-4 md:mt-0">
            <Button variant="ghost" style={{ color: '#67E8F9' }}>
              <MessageCircle className="ml-2 h-5 w-5" />
              گفتگو با پشتیبانی
            </Button>
          </div>
        </div>
      </div>
    </footer>
  );
}
